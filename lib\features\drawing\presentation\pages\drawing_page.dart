import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../services/localization_service.dart';

class DrawingPage extends ConsumerWidget {
  final bool fullscreen;

  const DrawingPage({super.key, this.fullscreen = false});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: fullscreen
          ? null
          : AppBar(
              title: Text(LocalizationService.get('draw')),
              actions: [
                IconButton(
                  onPressed: () {
                    // TODO: Implement save functionality
                  },
                  icon: const Icon(Icons.save),
                ),
                IconButton(
                  onPressed: () {
                    // TODO: Implement export functionality
                  },
                  icon: const Icon(Icons.share),
                ),
              ],
            ),
      body: Column(
        children: [
          // Drawing Tools Bar
          if (!fullscreen)
            Container(
              height: 60,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  _buildToolButton(Icons.edit, 'Pen', true),
                  _buildToolButton(Icons.auto_fix_high, 'Eraser', false),
                  _buildToolButton(Icons.crop_square, 'Rectangle', false),
                  _buildToolButton(Icons.circle_outlined, 'Circle', false),
                  _buildToolButton(Icons.text_fields, 'Text', false),
                  const Spacer(),
                  IconButton(
                    onPressed: () {
                      // TODO: Implement undo
                    },
                    icon: const Icon(Icons.undo),
                  ),
                  IconButton(
                    onPressed: () {
                      // TODO: Implement redo
                    },
                    icon: const Icon(Icons.redo),
                  ),
                ],
              ),
            ),

          // Drawing Canvas
          Expanded(
            child: Container(
              width: double.infinity,
              color: Colors.white,
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.draw,
                      size: 64,
                      color: Colors.grey,
                    ),
                    SizedBox(height: 16),
                    Text(
                      'Drawing Canvas',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Touch-based drawing engine will be implemented here',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: fullscreen
          ? FloatingActionButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Icon(Icons.fullscreen_exit),
            )
          : null,
    );
  }

  Widget _buildToolButton(IconData icon, String tooltip, bool isSelected) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: IconButton(
        onPressed: () {
          // TODO: Implement tool selection
        },
        icon: Icon(icon),
        tooltip: tooltip,
        style: IconButton.styleFrom(
          backgroundColor:
              isSelected ? Colors.blue.withValues(alpha: 0.1) : null,
          foregroundColor: isSelected ? Colors.blue : Colors.grey[600],
        ),
      ),
    );
  }
}
