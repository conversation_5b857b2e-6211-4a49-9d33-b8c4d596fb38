import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LocalizationService {
  static const String _languageKey = 'selected_language';
  static const String _countryKey = 'selected_country';
  
  static late SharedPreferences _prefs;
  
  static Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
  }
  
  static Locale get currentLocale {
    final languageCode = _prefs.getString(_languageKey) ?? 'en';
    final countryCode = _prefs.getString(_countryKey) ?? 'US';
    return Locale(languageCode, countryCode);
  }
  
  static Future<void> setLocale(Locale locale) async {
    await _prefs.setString(_languageKey, locale.languageCode);
    await _prefs.setString(_countryKey, locale.countryCode ?? '');
  }
  
  static bool get isEnglish => currentLocale.languageCode == 'en';
  static bool get isHindi => currentLocale.languageCode == 'hi';
  static bool get isGujarati => currentLocale.languageCode == 'gu';
  
  // Common translations for the app
  static const Map<String, Map<String, String>> _translations = {
    'en': {
      'app_name': 'Plan Builder',
      'home': 'Home',
      'draw': 'Draw',
      'projects': 'Projects',
      'templates': 'Templates',
      'ai_assistant': 'AI Assistant',
      'settings': 'Settings',
      'new_project': 'New Project',
      'recent_projects': 'Recent Projects',
      'create_floor_plan': 'Create Floor Plan',
      'ai_generate': 'AI Generate',
      'voice_input': 'Voice Input',
      'photo_input': 'Photo Input',
      'text_input': 'Text Input',
      'save': 'Save',
      'export': 'Export',
      'share': 'Share',
      'delete': 'Delete',
      'edit': 'Edit',
      'cancel': 'Cancel',
      'confirm': 'Confirm',
      'loading': 'Loading...',
      'error': 'Error',
      'success': 'Success',
      'welcome': 'Welcome to Plan Builder',
      'welcome_subtitle': 'Create professional floor plans with AI assistance',
    },
    'hi': {
      'app_name': 'प्लान बिल्डर',
      'home': 'होम',
      'draw': 'ड्रॉ',
      'projects': 'प्रोजेक्ट्स',
      'templates': 'टेम्प्लेट्स',
      'ai_assistant': 'AI असिस्टेंट',
      'settings': 'सेटिंग्स',
      'new_project': 'नया प्रोजेक्ट',
      'recent_projects': 'हाल के प्रोजेक्ट्स',
      'create_floor_plan': 'फ्लोर प्लान बनाएं',
      'ai_generate': 'AI जेनरेट',
      'voice_input': 'वॉयस इनपुट',
      'photo_input': 'फोटो इनपुट',
      'text_input': 'टेक्स्ट इनपुट',
      'save': 'सेव',
      'export': 'एक्सपोर्ट',
      'share': 'शेयर',
      'delete': 'डिलीट',
      'edit': 'एडिट',
      'cancel': 'कैंसल',
      'confirm': 'कन्फर्म',
      'loading': 'लोड हो रहा है...',
      'error': 'एरर',
      'success': 'सफल',
      'welcome': 'प्लान बिल्डर में आपका स्वागत है',
      'welcome_subtitle': 'AI सहायता के साथ पेशेवर फ्लोर प्लान बनाएं',
    },
    'gu': {
      'app_name': 'પ્લાન બિલ્ડર',
      'home': 'હોમ',
      'draw': 'ડ્રો',
      'projects': 'પ્રોજેક્ટ્સ',
      'templates': 'ટેમ્પ્લેટ્સ',
      'ai_assistant': 'AI આસિસ્ટન્ટ',
      'settings': 'સેટિંગ્સ',
      'new_project': 'નવો પ્રોજેક્ટ',
      'recent_projects': 'તાજેતરના પ્રોજેક્ટ્સ',
      'create_floor_plan': 'ફ્લોર પ્લાન બનાવો',
      'ai_generate': 'AI જનરેટ',
      'voice_input': 'વૉઇસ ઇનપુટ',
      'photo_input': 'ફોટો ઇનપુટ',
      'text_input': 'ટેક્સ્ટ ઇનપુટ',
      'save': 'સેવ',
      'export': 'એક્સપોર્ટ',
      'share': 'શેર',
      'delete': 'ડિલીટ',
      'edit': 'એડિટ',
      'cancel': 'કેન્સલ',
      'confirm': 'કન્ફર્મ',
      'loading': 'લોડ થઈ રહ્યું છે...',
      'error': 'એરર',
      'success': 'સફળ',
      'welcome': 'પ્લાન બિલ્ડરમાં આપનું સ્વાગત છે',
      'welcome_subtitle': 'AI સહાયતા સાથે વ્યાવસાયિક ફ્લોર પ્લાન બનાવો',
    },
  };
  
  static String translate(String key, [String? languageCode]) {
    final locale = languageCode ?? currentLocale.languageCode;
    return _translations[locale]?[key] ?? _translations['en']?[key] ?? key;
  }
  
  static String get(String key) => translate(key);
}
