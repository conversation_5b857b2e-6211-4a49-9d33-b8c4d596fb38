import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../core/router/app_router.dart';

// Theme Mode Provider
final themeModeProvider = StateNotifierProvider<ThemeModeNotifier, ThemeMode>(
  (ref) => ThemeModeNotifier(),
);

class ThemeModeNotifier extends StateNotifier<ThemeMode> {
  ThemeModeNotifier() : super(ThemeMode.light);

  void toggleTheme() {
    state = state == ThemeMode.light ? ThemeMode.dark : ThemeMode.light;
  }

  void setTheme(ThemeMode mode) {
    state = mode;
  }
}

// Locale Provider
final localeProvider = StateNotifierProvider<LocaleNotifier, Locale>(
  (ref) => LocaleNotifier(),
);

class LocaleNotifier extends StateNotifier<Locale> {
  LocaleNotifier() : super(const Locale('en', 'US'));

  void setLocale(Locale locale) {
    state = locale;
  }
}

// Router Provider
final routerProvider = Provider<GoRouter>((ref) {
  return AppRouter.router;
});

// Current User Provider
final currentUserProvider = StateProvider<String?>((ref) => null);

// Connectivity Provider
final connectivityProvider = StateProvider<bool>((ref) => true);

// Loading State Provider
final loadingProvider = StateProvider<bool>((ref) => false);

// Error Message Provider
final errorMessageProvider = StateProvider<String?>((ref) => null);

// Navigation Index Provider (for bottom navigation)
final navigationIndexProvider = StateProvider<int>((ref) => 0);

// Drawing Tool Provider
final selectedDrawingToolProvider = StateProvider<DrawingTool>((ref) => DrawingTool.pen);

enum DrawingTool {
  pen,
  eraser,
  line,
  rectangle,
  circle,
  text,
  select,
}

// Project Filter Provider
final projectFilterProvider = StateProvider<ProjectFilter>((ref) => ProjectFilter.all);

enum ProjectFilter {
  all,
  recent,
  favorites,
  completed,
  inProgress,
}

// Template Category Provider
final templateCategoryProvider = StateProvider<TemplateCategory>((ref) => TemplateCategory.residential);

enum TemplateCategory {
  residential,
  commercial,
  industrial,
  vastu,
}

// AI Feature Provider
final selectedAIFeatureProvider = StateProvider<AIFeature>((ref) => AIFeature.textToPlan);

enum AIFeature {
  textToPlan,
  voiceToPlan,
  photoToPlan,
  advisor,
}

// Export Format Provider
final exportFormatProvider = StateProvider<ExportFormat>((ref) => ExportFormat.pdf);

enum ExportFormat {
  pdf,
  jpg,
  png,
  dwg,
}

// Language Provider with specific support for Indian languages
final supportedLanguagesProvider = Provider<List<LanguageOption>>((ref) {
  return [
    LanguageOption(
      code: 'en',
      countryCode: 'US',
      name: 'English',
      nativeName: 'English',
    ),
    LanguageOption(
      code: 'hi',
      countryCode: 'IN',
      name: 'Hindi',
      nativeName: 'हिन्दी',
    ),
    LanguageOption(
      code: 'gu',
      countryCode: 'IN',
      name: 'Gujarati',
      nativeName: 'ગુજરાતી',
    ),
  ];
});

class LanguageOption {
  final String code;
  final String countryCode;
  final String name;
  final String nativeName;

  LanguageOption({
    required this.code,
    required this.countryCode,
    required this.name,
    required this.nativeName,
  });

  Locale get locale => Locale(code, countryCode);
}
