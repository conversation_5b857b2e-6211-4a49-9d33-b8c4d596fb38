import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../services/localization_service.dart';

class AIAssistantPage extends ConsumerWidget {
  const AIAssistantPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: Text(LocalizationService.get('ai_assistant')),
      ),
      body: Column(
        children: [
          // AI Features Grid
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                children: [
                  _buildAIFeatureCard(
                    context,
                    icon: Icons.text_fields,
                    title: LocalizationService.get('text_input'),
                    subtitle: 'Describe your floor plan',
                    color: Colors.blue,
                    onTap: () {
                      // TODO: Implement text to plan
                    },
                  ),
                  _buildAIFeatureCard(
                    context,
                    icon: Icons.mic,
                    title: LocalizationService.get('voice_input'),
                    subtitle: 'Speak your requirements',
                    color: Colors.green,
                    onTap: () {
                      // TODO: Implement voice to plan
                    },
                  ),
                  _buildAIFeatureCard(
                    context,
                    icon: Icons.camera_alt,
                    title: LocalizationService.get('photo_input'),
                    subtitle: 'Upload a sketch or photo',
                    color: Colors.orange,
                    onTap: () {
                      // TODO: Implement photo to plan
                    },
                  ),
                  _buildAIFeatureCard(
                    context,
                    icon: Icons.help_outline,
                    title: 'AI Advisor',
                    subtitle: 'Get construction advice',
                    color: Colors.purple,
                    onTap: () {
                      // TODO: Implement AI advisor
                    },
                  ),
                ],
              ),
            ),
          ),

          // Chat Interface (placeholder)
          Container(
            height: 200,
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(12),
                    ),
                  ),
                  child: const Row(
                    children: [
                      Icon(Icons.smart_toy, color: Colors.blue),
                      SizedBox(width: 8),
                      Text(
                        'AI Assistant Chat',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                const Expanded(
                  child: Center(
                    child: Text(
                      'AI chat interface will be implemented here',
                      style: TextStyle(
                        color: Colors.grey,
                      ),
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.all(8),
                  child: Row(
                    children: [
                      Expanded(
                        child: TextField(
                          decoration: InputDecoration(
                            hintText: 'Ask AI assistant...',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(20),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      IconButton(
                        onPressed: () {
                          // TODO: Send message to AI
                        },
                        icon: const Icon(Icons.send),
                        style: IconButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAIFeatureCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
