import 'dart:typed_data';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:firebase_analytics/firebase_analytics.dart';

class FirebaseService {
  static FirebaseAuth get auth => FirebaseAuth.instance;
  static FirebaseFirestore get firestore => FirebaseFirestore.instance;
  static FirebaseStorage get storage => FirebaseStorage.instance;
  static FirebaseRemoteConfig get remoteConfig => FirebaseRemoteConfig.instance;
  static FirebaseAnalytics get analytics => FirebaseAnalytics.instance;

  static Future<void> initialize() async {
    try {
      // Initialize Remote Config
      await _initializeRemoteConfig();

      // Initialize Analytics
      await analytics.setAnalyticsCollectionEnabled(true);

      // Set up Firestore settings
      firestore.settings = const Settings(
        persistenceEnabled: true,
        cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED,
      );

      print('Firebase services initialized successfully');
    } catch (e) {
      print('Error initializing Firebase services: $e');
    }
  }

  static Future<void> _initializeRemoteConfig() async {
    try {
      await remoteConfig.setConfigSettings(
        RemoteConfigSettings(
          fetchTimeout: const Duration(minutes: 1),
          minimumFetchInterval: const Duration(hours: 1),
        ),
      );

      // Set default values
      await remoteConfig.setDefaults({
        'gemini_api_keys': '[]',
        'ads_enabled': true,
        'local_ads_enabled': true,
        'max_projects_free': 5,
        'premium_features_enabled': false,
        'maintenance_mode': false,
        'app_version_required': '1.0.0',
      });

      // Fetch and activate
      await remoteConfig.fetchAndActivate();
    } catch (e) {
      print('Error initializing Remote Config: $e');
    }
  }

  // Authentication Methods
  static Future<User?> signInAnonymously() async {
    try {
      final credential = await auth.signInAnonymously();
      await analytics.logLogin(loginMethod: 'anonymous');
      return credential.user;
    } catch (e) {
      print('Error signing in anonymously: $e');
      return null;
    }
  }

  static Future<void> signOut() async {
    try {
      await auth.signOut();
      await analytics.logEvent(name: 'user_logout');
    } catch (e) {
      print('Error signing out: $e');
    }
  }

  // Firestore Methods
  static Future<void> saveProject(Map<String, dynamic> projectData) async {
    try {
      final user = auth.currentUser;
      if (user == null) return;

      await firestore
          .collection('users')
          .doc(user.uid)
          .collection('projects')
          .add({
        ...projectData,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      await analytics.logEvent(
        name: 'project_saved',
        parameters: {'project_type': projectData['type'] ?? 'unknown'},
      );
    } catch (e) {
      print('Error saving project: $e');
    }
  }

  static Stream<QuerySnapshot> getUserProjects() {
    final user = auth.currentUser;
    if (user == null) {
      return const Stream.empty();
    }

    return firestore
        .collection('users')
        .doc(user.uid)
        .collection('projects')
        .orderBy('updatedAt', descending: true)
        .snapshots();
  }

  // Storage Methods
  static Future<String?> uploadImage(String path, List<int> imageData) async {
    try {
      final user = auth.currentUser;
      if (user == null) return null;

      final ref = storage.ref().child('users/${user.uid}/images/$path');
      final uploadTask = ref.putData(Uint8List.fromList(imageData));
      final snapshot = await uploadTask;

      return await snapshot.ref.getDownloadURL();
    } catch (e) {
      print('Error uploading image: $e');
      return null;
    }
  }

  // Remote Config Methods
  static List<String> getGeminiApiKeys() {
    try {
      final keysJson = remoteConfig.getString('gemini_api_keys');
      // Parse JSON array of API keys
      return []; // TODO: Implement JSON parsing
    } catch (e) {
      print('Error getting Gemini API keys: $e');
      return [];
    }
  }

  static bool get adsEnabled => remoteConfig.getBool('ads_enabled');
  static bool get localAdsEnabled => remoteConfig.getBool('local_ads_enabled');
  static int get maxProjectsFree => remoteConfig.getInt('max_projects_free');
  static bool get premiumFeaturesEnabled =>
      remoteConfig.getBool('premium_features_enabled');
  static bool get maintenanceMode => remoteConfig.getBool('maintenance_mode');

  // Analytics Methods
  static Future<void> logScreenView(String screenName) async {
    await analytics.logScreenView(screenName: screenName);
  }

  static Future<void> logFeatureUsed(String featureName) async {
    await analytics.logEvent(
      name: 'feature_used',
      parameters: {'feature_name': featureName},
    );
  }

  static Future<void> logError(String error, String? stackTrace) async {
    await analytics.logEvent(
      name: 'app_error',
      parameters: {
        'error_message': error,
        'stack_trace': stackTrace ?? 'No stack trace',
      },
    );
  }
}
