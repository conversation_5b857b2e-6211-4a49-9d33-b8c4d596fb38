import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../providers/app_providers.dart';

class MainScaffold extends ConsumerWidget {
  final Widget child;

  const MainScaffold({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentIndex = ref.watch(navigationIndexProvider);

    return Scaffold(
      body: child,
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: currentIndex,
        onTap: (index) {
          ref.read(navigationIndexProvider.notifier).state = index;
          _navigateToPage(context, index);
        },
        type: BottomNavigationBarType.fixed,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home_outlined),
            activeIcon: Icon(Icons.home),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.draw_outlined),
            activeIcon: Icon(Icons.draw),
            label: 'Draw',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.folder_outlined),
            activeIcon: Icon(Icons.folder),
            label: 'Projects',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.description_outlined),
            activeIcon: Icon(Icons.description),
            label: 'Templates',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.smart_toy_outlined),
            activeIcon: Icon(Icons.smart_toy),
            label: 'AI',
          ),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(context, currentIndex),
    );
  }

  void _navigateToPage(BuildContext context, int index) {
    switch (index) {
      case 0:
        context.go('/home');
        break;
      case 1:
        context.go('/drawing');
        break;
      case 2:
        context.go('/projects');
        break;
      case 3:
        context.go('/templates');
        break;
      case 4:
        context.go('/ai');
        break;
    }
  }

  Widget? _buildFloatingActionButton(BuildContext context, int currentIndex) {
    switch (currentIndex) {
      case 0: // Home
        return FloatingActionButton(
          onPressed: () => context.go('/drawing'),
          tooltip: 'Start New Project',
          child: const Icon(Icons.add),
        );
      case 1: // Drawing
        return FloatingActionButton.extended(
          onPressed: () => context.go('/drawing/fullscreen'),
          icon: const Icon(Icons.fullscreen),
          label: const Text('Fullscreen'),
        );
      case 2: // Projects
        return FloatingActionButton(
          onPressed: () => context.go('/drawing'),
          tooltip: 'New Project',
          child: const Icon(Icons.add),
        );
      default:
        return null;
    }
  }
}
