import 'package:go_router/go_router.dart';
import 'package:flutter/material.dart';
import '../../features/home/<USER>/pages/home_page.dart';
import '../../features/drawing/presentation/pages/drawing_page.dart';
import '../../features/projects/presentation/pages/projects_page.dart';
import '../../features/templates/presentation/pages/templates_page.dart';
import '../../features/ai/presentation/pages/ai_assistant_page.dart';
import '../../features/export/presentation/pages/export_page.dart';
import '../../features/settings/presentation/pages/settings_page.dart';
import '../../shared/widgets/main_scaffold.dart';

class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: '/home',
    routes: [
      ShellRoute(
        builder: (context, state, child) {
          return MainScaffold(child: child);
        },
        routes: [
          GoRoute(
            path: '/home',
            name: 'home',
            builder: (context, state) => const HomePage(),
          ),
          GoRoute(
            path: '/drawing',
            name: 'drawing',
            builder: (context, state) => const DrawingPage(),
          ),
          GoRoute(
            path: '/projects',
            name: 'projects',
            builder: (context, state) => const ProjectsPage(),
          ),
          GoRoute(
            path: '/templates',
            name: 'templates',
            builder: (context, state) => const TemplatesPage(),
          ),
          GoRoute(
            path: '/ai',
            name: 'ai',
            builder: (context, state) => const AIAssistantPage(),
          ),
          GoRoute(
            path: '/export',
            name: 'export',
            builder: (context, state) => const ExportPage(),
          ),
          GoRoute(
            path: '/settings',
            name: 'settings',
            builder: (context, state) => const SettingsPage(),
          ),
        ],
      ),
      // Full-screen routes (without main scaffold)
      GoRoute(
        path: '/drawing/fullscreen',
        name: 'drawing-fullscreen',
        builder: (context, state) => const DrawingPage(fullscreen: true),
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(
        title: const Text('Page Not Found'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            const Text(
              'Page Not Found',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'The page "${state.uri}" could not be found.',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go('/home'),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    ),
  );
}
